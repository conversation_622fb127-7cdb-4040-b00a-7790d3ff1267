[metadata]
name = trackeval
version = 1.0.dev1
author = <PERSON><PERSON><PERSON>, <PERSON><PERSON>
author_email = j<PERSON><PERSON><PERSON><EMAIL>
description = Code for evaluating object tracking
long_description = file: Readme.md
long_description_content_type = text/markdown
url = https://github.com/JonathonLuiten/TrackEval
project_urls =
    Bug Tracker = https://github.com/JonathonLuiten/TrackEval/issues
classifiers =
    Programming Language :: Python :: 3
    Programming Language :: Python :: 3 :: Only
    License :: OSI Approved :: MIT License
    Operating System :: OS Independent
    Topic :: Scientific/Engineering
license_files = LICENSE

[options]
install_requires =
    numpy
    scipy
packages = find:

[options.packages.find]
include = trackeval*
